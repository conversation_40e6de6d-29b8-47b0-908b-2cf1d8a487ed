# Utilise une image Node légère
FROM node:18-alpine

# C<PERSON>e le répertoire de travail
WORKDIR /app

# Copie les fichiers package.json et package-lock.json depuis le sous-dossier react-app
COPY react-app/package*.json ./

# Installation des dépendances
RUN npm install

# Copie tout le contenu du projet React
COPY react-app/ ./

# Port sur lequel l'app React tourne
EXPOSE 3000

# Lancement de l'application React
CMD ["npm", "run", "dev"]
