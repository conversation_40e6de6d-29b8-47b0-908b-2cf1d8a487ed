<?php

namespace App\Http\Controllers;

use App\Http\Requests\MambraRequest;
use Carbon\Carbon;
use App\Models\Mambra;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\MambraResource;

class MambraController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $mambra = Mambra::all();
        return MambraResource::collection($mambra);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MambraRequest $request)
    {
        $c = DB::transaction(function () use ($request) {
            $mambra = new Mambra ([
            'nom' => $request->get('nom'),
            'prenom' => $request->get('prenom'),
            'date_naissance' => Carbon::parse($request->get('dateNaissance'))->format('Y-m-d'),
            'sexe' => $request->get('sexe'),
            'date_bapteme' => Carbon::parse($request->get('dateBapteme'))->format('Y-m-d'),
            'telephone' => $request->get('telephone'),
            'situation_matrimoniale' => $request->get('situationMatrimoniale'),
            'occupation' => $request->get('occupation'), 
            'observation' => $request->get('observation'),
        ]);
            $mambra->saveOrFail();
            return $mambra;
        });

        return MambraResource::make($c);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MambraRequest $request, Mambra $mambra)
    {

        $mambra->update($request->all());
        return MambraResource::make($mambra);

    }


    /**
     * Display the specified resource.
     */
    public function show(Mambra $mambra)
    {
        return MambraResource::make($mambra);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Mambra $mambra)
    {
        $mambra->delete();
        $mambras = Mambra::all();
        return MambraResource::collection($mambras);

    }
}
