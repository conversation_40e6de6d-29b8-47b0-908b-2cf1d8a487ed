<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class KilasyRequest extends FormRequest{

    public function authorize(){
        return true;
    }

    public function rules(){
        $rules = [
            'nom'                             => ['required', 'string', 'max:255'], 
            'libelle'                         => ['nullable', 'string', 'max:255'], 
            'description'                     => ['nullable', 'string'],

        ];

        return $rules;

    }


}



?>