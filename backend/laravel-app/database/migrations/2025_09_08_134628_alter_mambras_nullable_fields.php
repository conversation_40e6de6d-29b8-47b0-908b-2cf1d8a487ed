<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('mambras', function (Blueprint $table) {
            $table->date('date_naissance')->nullable()->change();
            $table->date('date_bapteme')->nullable()->change();
            $table->string('sexe')->nullable()->change();
            $table->string('telephone')->nullable()->change();
            $table->string('situation_matrimoniale')->nullable()->change();
            $table->string('occupation')->nullable()->change();
            $table->string('observation')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('mambras', function (Blueprint $table) {
            $table->date('date_naissance')->nullable(false)->change();
            $table->date('date_bapteme')->nullable(false)->change();
            $table->string('sexe')->nullable(false)->change();
            $table->string('telephone')->nullable(false)->change();
            $table->string('situation_matrimoniale')->nullable(false)->change();
            $table->string('occupation')->nullable(false)->change();
            $table->string('observation')->nullable(false)->change();
        });
    }
};
