FROM php:8.2-apache

# Install dependencies
RUN apt-get update && apt-get install -y \
    zip unzip git curl libonig-dev libzip-dev libxml2-dev \
    libpng-dev libjpeg-dev libfreetype6-dev libpq-dev \
    libmariadb-dev && \
    docker-php-ext-install pdo pdo_mysql zip

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Set Apache to serve Laravel public folder
RUN sed -i 's|/var/www/html|/var/www/html/laravel-app/public|g' /etc/apache2/sites-available/000-default.conf

# Set working directory
WORKDIR /var/www/html/laravel-app

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy Laravel code
COPY ./laravel-app /var/www/html/laravel-app

# Permissions
RUN mkdir -p storage bootstrap/cache && \
    chown -R www-data:www-data . && \
    chmod -R 775 storage bootstrap/cache
